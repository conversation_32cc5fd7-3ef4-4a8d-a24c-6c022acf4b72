-- Metal Detector Items for ESX
-- Add these items to your items table

INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES
('detector1', 'Basic Metal Detector', 2000, 0, 1),
('detector2', 'Advanced Metal Detector', 2200, 0, 1),
('detector3', 'Professional Metal Detector', 2400, 0, 1),
('detector4', 'Expert Metal Detector', 2600, 0, 1),
('detector5', 'Master Metal Detector', 2800, 0, 1);

-- Metal and mineral items
INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES
('metalscrap', 'Metal Scrap', 100, 0, 1),
('steel', 'Steel', 150, 0, 1),
('iron', 'Iron Ore', 120, 0, 1),
('copper', 'Copper Ore', 110, 0, 1),
('aluminum', 'Aluminum', 80, 0, 1),
('gold', 'Gold Nugget', 50, 0, 1),
('silver', 'Silver Ore', 60, 0, 1),
('electronics', 'Electronic Parts', 200, 0, 1),
('diamond', 'Diamond', 10, 1, 1),
('emerald', 'Emerald', 15, 1, 1),
('ruby', 'Ruby', 12, 1, 1),
('sapphire', 'Sapphire', 13, 1, 1);
