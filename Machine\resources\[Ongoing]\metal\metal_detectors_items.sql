-- Metal Detector Items & Rewards for ESX with ox_inventory support
-- Import this file to your database

-- Metal Detector Items for ESX (Level 1-5)
INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`, `description`) VALUES
('detector1', 'Basic Metal Detector', 2000, 0, 1, 'Entry level detector - finds money and basic materials'),
('detector2', 'Advanced Metal Detector', 2200, 0, 1, 'Steel-focused detector with better sensitivity'),
('detector3', 'Professional Metal Detector', 2400, 0, 1, 'Finds copper and aluminum materials'),
('detector4', 'Expert Metal Detector', 2600, 0, 1, 'Precious metals detector - gold and silver'),
('detector5', 'Master Metal Detector', 2800, 0, 1, 'Ultimate detector - diamonds and rare materials');

-- Reward Items for Level-Based Mining System
INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`, `description`) VALUES
-- Basic Materials (Level 1-2)
('steel', 'Steel', 100, 0, 1, 'High quality steel material'),
('metalscrap', 'Metal Scrap', 50, 0, 1, 'Scrap metal pieces'),

-- Intermediate Materials (Level 3)
('copper', 'Copper', 80, 0, 1, 'Pure copper material'),
('aluminum', 'Aluminum', 60, 0, 1, 'Lightweight aluminum'),

-- Precious Materials (Level 4-5)
('silver', 'Silver', 150, 0, 1, 'Precious silver metal'),
('gold', 'Gold', 200, 0, 1, 'Pure gold nugget'),
('diamond', 'Diamond', 50, 1, 1, 'Rare diamond gem'),
('goldbar', 'Gold Bar', 500, 1, 1, 'Refined gold bar');

-- Alternative for older ESX versions (if above doesn't work, use this instead)
-- INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES
-- ('detector1', 'Basic Metal Detector', 2000, 0, 1),
-- ('detector2', 'Advanced Metal Detector', 2200, 0, 1),
-- ('detector3', 'Professional Metal Detector', 2400, 0, 1),
-- ('detector4', 'Expert Metal Detector', 2600, 0, 1),
-- ('detector5', 'Master Metal Detector', 2800, 0, 1),
-- ('steel', 'Steel', 100, 0, 1),
-- ('metalscrap', 'Metal Scrap', 50, 0, 1),
-- ('copper', 'Copper', 80, 0, 1),
-- ('aluminum', 'Aluminum', 60, 0, 1),
-- ('silver', 'Silver', 150, 0, 1),
-- ('gold', 'Gold', 200, 0, 1),
-- ('diamond', 'Diamond', 50, 1, 1),
-- ('goldbar', 'Gold Bar', 500, 1, 1);
