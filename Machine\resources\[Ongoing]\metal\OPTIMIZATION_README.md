# 🔧 Metal Detector Script - OPTIMIZED VERSION

## ⚡ **OPTIMASI PERFORMA YANG DILAKUKAN**

### **🖥️ CLIENT-SIDE OPTIMIZATIONS:**

1. **Reduced Wait(0) Loops** - Mengurangi loop berat yang menyebabkan lag
   - `EnsureAnimDict()` & `EnsureModel()`: Wait(0) → Wait(10) + timeout
   - Main prospecting loop: Wait(0) → Wait(50)
   - Marker rendering: Wait(0) → Wait(16) (~60fps)

2. **Target Processing Optimization**
   - Limit processing per frame: max 50 targets
   - Location updater: 10s → 5s interval
   - Early break untuk mencegah frame drops

3. **Memory Management**
   - Timeout system untuk loading assets
   - Optimized coordinate calculations

### **🖥️ SERVER-SIDE OPTIMIZATIONS:**

1. **Batch Coordinate Generation**
   - Generate coordinates dalam batch (50 per batch)
   - Delay 100ms antar batch untuk mencegah server freeze
   - Reduced total locations: 300 → 150

2. **Enhanced Detector System**
   - Store detector type dengan player status
   - Optimized reward calculation
   - Level-based multipliers dan bonuses

3. **Smart Item Registration**
   - Auto-detect ox_inventory vs standard ESX
   - Register semua detector levels (1-5)
   - Proper error handling

## 🎮 **SISTEM DETECTOR LEVEL 1-5**

### **📊 Detector Specifications:**

| Level | Name | Multiplier | Bonus Chance | Range | Description |
|-------|------|------------|--------------|-------|-------------|
| 1 | Basic Metal Detector | 1.0x | 0% | 50m | Entry level |
| 2 | Advanced Metal Detector | 1.2x | +5% | 75m | Improved sensitivity |
| 3 | Professional Metal Detector | 1.5x | +10% | 100m | Professional grade |
| 4 | Expert Metal Detector | 1.8x | +15% | 125m | Expert level |
| 5 | Master Metal Detector | 2.0x | +20% | 150m | Ultimate detector |

### **🎁 Enhanced Reward System:**

- **Level-based Epic Chance**: +1.5% per level
- **Level-based Rare Chance**: +3% per level  
- **Bonus Items**: Level 3+ dapat bonus items (chance: level × 4%)
- **Smart Notifications**: ox_lib notifications dengan detector info

## 🗺️ **SINGLE LOCATION SYSTEM**

### **📍 Optimized Mining Zone:**
- **Location**: Sandy Shores Mining Site
- **Coordinates**: 2141.6426, 3835.1187, 30.3905
- **Zone Size**: 200m radius (optimized)
- **Detection Points**: 150 (reduced untuk performa)
- **Blip**: Mining icon dengan dark yellow color

## 🔧 **INSTALLATION & SETUP**

### **1. Database Setup:**
```sql
-- Import metal_detectors.sql
source metal_detectors.sql
```

### **2. Resource Configuration:**
```lua
-- config.lua sudah dioptimasi untuk:
Config.Core = "ESX"
Config.PlayerLoadedEvent = "esx:playerLoaded"
Config.DetectorItem = "detector1"
```

### **3. Dependencies:**
- ESX (r_core variant)
- ox_inventory (optional, auto-detected)
- ox_lib (untuk notifications)

### **4. Give Items:**
```
/giveitem [id] detector1 1
/giveitem [id] detector2 1
/giveitem [id] detector3 1
/giveitem [id] detector4 1
/giveitem [id] detector5 1
```

## 📈 **PERFORMANCE IMPROVEMENTS**

### **Before Optimization:**
- ❌ Wait(0) loops causing high CPU usage
- ❌ 300+ detection points causing lag
- ❌ Single detector type only
- ❌ Basic reward system

### **After Optimization:**
- ✅ Optimized timing (50ms, 16ms intervals)
- ✅ 150 detection points with batch generation
- ✅ 5 detector levels with progressive rewards
- ✅ Enhanced notification system
- ✅ ox_inventory full support
- ✅ Smart resource management

## 🐛 **DEBUG MODE**

Enable debug mode untuk monitoring:
```
setr metal_debug 1
```

Debug info akan menampilkan:
- Player ID
- Detector level used
- Items found dengan rarity
- Multiplier applied

## 🎯 **USAGE TIPS**

1. **Start dengan detector1** untuk pemula
2. **Upgrade ke level tinggi** untuk reward lebih besar
3. **Gunakan di Sandy Shores** untuk hasil optimal
4. **Monitor server performance** dengan debug mode
5. **Restart resource** setelah config changes

---
**🔥 Script telah dioptimasi untuk performa maksimal dengan sistem detector 5 level di 1 lokasi!**
