-----------------For support, scripts, and more----------------
--------------- https://discord.gg/VGYkkAYVv2  -------------
---------------------------------------------------------------

QBCore, ESX = nil, nil

if Config.Core == "QBCore" then
    QBCore = exports['qb-core']:GetCoreObject()
elseif Config.Core == "ESX" then
    ESX = exports['r_core']:getSharedObject()
end

local PROSPECTING_STATUS = {}
local PROSPECTING_TARGETS = {}

local PROSPECTING_DIFFICULTIES = {}

--[[ Common ]]
function UpdateProspectingTargets(player)
    local targets = {}
    for _, target in next, PROSPECTING_TARGETS do
        local difficulty = PROSPECTING_DIFFICULTIES[target.resource] or 1.0
        targets[#targets + 1] = {target.x, target.y, target.z, difficulty}
    end
    TriggerClientEvent("prospecting:setTargetPool", player, targets)
end

function InsertProspectingTarget(resource, x, y, z, data)
    PROSPECTING_TARGETS[#PROSPECTING_TARGETS + 1] = {resource = resource, data = data, x = x, y = y, z = z}
end

function InsertProspectingTargets(resource, targets)
    for _, target in next, targets do
        InsertProspectingTarget(resource, target.x, target.y, target.z, target.data)
    end
end

local function RemoveTargetIndex(coords)
    for index, target in next, PROSPECTING_TARGETS do
        local dx, dy, dz = target.x, target.y, target.z
        if math.floor(dx) == math.floor(coords.x) and math.floor(dy) == math.floor(coords.y) and math.floor(dz) == math.floor(coords.z) then
            table.remove(PROSPECTING_TARGETS, index)
            break
        end
    end
end

function RemoveProspectingTarget(coords)
    RemoveTargetIndex(coords)
    TriggerClientEvent("prospecting:client:removeTarget", -1, coords)
end

function FindMatchingPickup(x, y, z)
    for index, target in next, PROSPECTING_TARGETS do
        local dx, dy, dz = target.x, target.y, target.z
        if math.floor(dx) == math.floor(x) and math.floor(dy) == math.floor(y) and math.floor(dz) == math.floor(z) then
            return index
        end
    end
    return nil
end

function HandleProspectingPickup(player, index, x, y, z)
    local target = PROSPECTING_TARGETS[index]
    if target then
        local dx, dy, dz = target.x, target.y, target.z
        local resource, data = target.resource, target.data
        if math.floor(dx) == math.floor(x) and math.floor(dy) == math.floor(y) and math.floor(dz) == math.floor(z) then
            RemoveProspectingTarget(vec3(x, y, z))
            OnCollected(player, resource, data, x, y, z)
        else
            local newMatch = FindMatchingPickup(x, y, z)
            if newMatch then
                HandleProspectingPickup(player, newMatch, x, y, z)
            end
        end
    else
    end
end

local function AddItem(id, name, amount)
    if Config.Core == "QBCore" then
        local Player = QBCore.Functions.GetPlayer(id)
        Player.Functions.AddItem(name, amount)
        TriggerClientEvent("inventory:client:ItemBox", id, QBCore.Shared.Items[name], "add")
    elseif Config.Core == "ESX" then
        local xPlayer = ESX.GetPlayerFromId(id)
        xPlayer.addInventoryItem(name, amount)
    end
end

-- FIXED: Level-based reward system - setiap level detector dapat reward berbeda
function OnCollected(player, resource, data, x, y, z)
    -- Get player's detector type and level
    local detectorType = GetPlayerDetector(player) or "detector1"
    local detectorConfig = Config.Detectors[detectorType] or Config.Detectors["detector1"]

    -- Extract detector level (1-5) from detector name
    local detectorLevel = tonumber(string.match(detectorType, "%d+")) or 1

    -- Use level-specific rewards instead of generic ones
    local levelKey = "level" .. detectorLevel
    local levelRewards = Config.Items[data][levelKey]

    if not levelRewards then
        print("[Metal Detector ERROR] No rewards found for " .. levelKey .. " in " .. data)
        return
    end

    local items = {}
    math.randomseed(os.time() + player + os.clock())
    local randomizer = math.random(1, 100)

    -- Enhanced chance calculation based on detector level
    local epicChance = Config.Chances.epic + (detectorLevel * 1.5) -- +1.5% per level
    local rareChance = Config.Chances.rare + (detectorLevel * 3) -- +3% per level

    -- Determine rarity with level-based bonuses
    local selectedRarity = "common"
    if randomizer <= epicChance then
        selectedRarity = "epic"
    elseif randomizer <= rareChance then
        selectedRarity = "rare"
    end

    -- Get items from level-specific rewards
    items = levelRewards[selectedRarity] or levelRewards["common"] or Config.DefaultItems

    -- Select random item from rarity pool
    local item = items[math.random(1, #items)]
    local finalAmount = math.random(item.min, item.max)

    -- Add item to player
    AddItem(player, item.name, finalAmount)

    -- Enhanced notification with detector level info
    if Config.Core == "ESX" then
        local detectorName = detectorConfig.name or ("Level " .. detectorLevel .. " Detector")

        if GetResourceState('ox_lib') == 'started' then
            TriggerClientEvent('ox_lib:notify', player, {
                title = '🔍 ' .. detectorName,
                description = string.format('Found %dx %s (%s)', finalAmount, item.name, selectedRarity:upper()),
                type = selectedRarity == "epic" and "success" or (selectedRarity == "rare" and "inform" or "success"),
                duration = 4000,
                position = 'top-right'
            })
        else
            local xPlayer = ESX.GetPlayerFromId(player)
            if xPlayer then
                xPlayer.showNotification(string.format('🔍 Level %d: Found %dx %s', detectorLevel, finalAmount, item.name))
            end
        end
    end

    -- Debug logging
    if GetConvarInt('metal_debug', 0) == 1 then
        print(string.format("[Metal Detector] Player %d | %s (Level %d) | Found %dx %s (%s)",
            player, detectorType, detectorLevel, finalAmount, item.name, selectedRarity))
    end
end

--[[ Export handling ]]

function AddProspectingTarget(x, y, z, data)
    local resource = GetInvokingResource()
    InsertProspectingTarget(resource, x, y, z, data)
end

function AddProspectingTargets(list)
    local resource = GetInvokingResource()
    InsertProspectingTargets(resource, list)

end

function StartProspecting(player, detectorType)
    if not PROSPECTING_STATUS[player] then
        -- Store detector type with player status for reward calculation
        PROSPECTING_STATUS[player] = {
            startTime = GetGameTimer(),
            detector = detectorType or "detector1"
        }
        TriggerClientEvent("prospecting:forceStart", player)
    end
end
AddEventHandler("prospecting:StartProspecting", function(player, detectorType)
    StartProspecting(player, detectorType)
end)

function StopProspecting(player)
    if PROSPECTING_STATUS[player] then
        PROSPECTING_STATUS[player] = nil
        TriggerClientEvent("prospecting:forceStop", player)
    end
end
AddEventHandler("prospecting:StopProspecting", function(player)
    StopProspecting(player)
end)

function IsProspecting(player)
    return PROSPECTING_STATUS[player] ~= nil
end

-- Get player's current detector type
function GetPlayerDetector(player)
    if PROSPECTING_STATUS[player] and PROSPECTING_STATUS[player].detector then
        return PROSPECTING_STATUS[player].detector
    end
    return "detector1" -- Default fallback
end

function SetDifficulty(modifier)
    local resource = GetInvokingResource()
    PROSPECTING_DIFFICULTIES[resource] = modifier
end

--[[ Client triggered events ]]

-- When the client stops prospecting
RegisterServerEvent("prospecting:userStoppedProspecting")
AddEventHandler("prospecting:userStoppedProspecting", function()
    local player = source
    if PROSPECTING_STATUS[player] then
        local time = GetGameTimer() - PROSPECTING_STATUS[player].startTime
        PROSPECTING_STATUS[player] = nil
        TriggerEvent("prospecting:onStop", player, time)
    end
end)

-- When the client starts prospecting
RegisterServerEvent("prospecting:userStartedProspecting")
AddEventHandler("prospecting:userStartedProspecting", function(detectorType)
    local player = source
    if not PROSPECTING_STATUS[player] then
        PROSPECTING_STATUS[player] = {
            startTime = GetGameTimer(),
            detector = detectorType or "detector1"
        }
        TriggerEvent("prospecting:onStart", player)
    end
end)

-- When the client collects a node
-- RegisterServerEvent("prospecting:userCollectedNode")
-- AddEventHandler("prospecting:userCollectedNode", function(index, x, y, z)
lib.callback.register("prospecting:userCollectedNode", function(source, index, x, y, z)
    local player = source
    if PROSPECTING_STATUS[player] then
        HandleProspectingPickup(player, index, x, y, z)
    end
end)

RegisterServerEvent("prospecting:userRequestsLocations")
AddEventHandler("prospecting:userRequestsLocations", function()
    local player = source
    UpdateProspectingTargets(player)
end)

-- thread to setup prospecting target at server start

--command to start and stop prospecting

-- OPTIMIZED: Register all detector levels (1-5) with proper ox_inventory support
CreateThread(function()
    Wait(1000) -- Wait for ESX to load

    if Config.Core == "QBCore" then
        -- QBCore support for all detector levels
        for detectorName, detectorData in pairs(Config.Detectors) do
            QBCore.Functions.CreateUseableItem(detectorName, function(source, item)
                if Prospecting.IsProspecting(source) then
                    Prospecting.StopProspecting(source)
                else
                    Prospecting.StartProspecting(source, detectorName)
                end
            end)
        end
    elseif Config.Core == "ESX" then
        -- Check if ox_inventory is available
        if GetResourceState('ox_inventory') == 'started' then
            -- ox_inventory support
            exports.ox_inventory:registerHook('useItem', function(playerId, itemName, slotId, metadata)
                -- Check if it's one of our detectors
                if Config.Detectors[itemName] then
                    if Prospecting.IsProspecting(playerId) then
                        Prospecting.StopProspecting(playerId)
                    else
                        Prospecting.StartProspecting(playerId, itemName)
                    end
                    return false -- Don't consume the item
                end
            end, {
                itemFilter = {
                    detector1 = true,
                    detector2 = true,
                    detector3 = true,
                    detector4 = true,
                    detector5 = true,
                }
            })
            print("[Metal Detector] Registered ox_inventory hooks for all detector levels")
        else
            -- Standard ESX support for all detector levels
            for detectorName, detectorData in pairs(Config.Detectors) do
                ESX.RegisterUsableItem(detectorName, function(source)
                    if Prospecting.IsProspecting(source) then
                        Prospecting.StopProspecting(source)
                    else
                        Prospecting.StartProspecting(source, detectorName)
                    end
                end)
            end
            print("[Metal Detector] Registered standard ESX items for all detector levels")
        end
    end
end)


CreateThread(function()
    for k, v in pairs(Config.Zones) do
        GenerateCoords(v.coords, v.data, v.zoneSize, v.zoneLocations)
    end
end)


-- OPTIMIZED: Generate coordinates in batches to reduce server load
function GenerateCoords(coords, data, zoneSize, zoneLocations)
    local coordslist = {}

    -- Pre-calculate random seed for better performance
    math.randomseed(os.time() + coords.x + coords.y)

    -- Generate coordinates in batches
    local batchSize = 50
    local totalBatches = math.ceil(zoneLocations / batchSize)

    CreateThread(function()
        for batch = 1, totalBatches do
            local currentBatchSize = math.min(batchSize, zoneLocations - (batch - 1) * batchSize)
            local batchCoords = {}

            for i = 1, currentBatchSize do
                local modX = math.random(-zoneSize, zoneSize)
                local modY = math.random(-zoneSize, zoneSize)
                local coordX = coords.x + modX
                local coordY = coords.y + modY

                batchCoords[#batchCoords + 1] = {
                    x = coordX,
                    y = coordY,
                    z = coords.z,
                    data = data
                }
            end

            -- Add batch to targets
            AddProspectingTargets(batchCoords)

            -- Small delay between batches to prevent server freeze
            if batch < totalBatches then
                Wait(100)
            end
        end

        print(string.format("[Metal Detector] Generated %d mining locations for %s", zoneLocations, data))
    end)
end