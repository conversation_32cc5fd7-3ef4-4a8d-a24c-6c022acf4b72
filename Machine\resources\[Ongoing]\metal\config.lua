-----------------For support, scripts, and more----------------
--------------- https://discord.gg/VGYkkAYVv2  -------------
---------------------------------------------------------------

Config = {}

Config.Core = "ESX" -- ESX or QBCore
Config.PlayerLoadedEvent = "esx:playerLoaded" -- esx:playerLoaded || QBCore:Client:OnPlayerLoaded

Config.ShowBlip = true -- show blip on map

Config.Chances = {
    ["common"] = 100, -- 100%
    ["rare"] = 15, -- 15%
    ["epic"] = 5, -- 5%
}
Config.ShowDrawMaker = true -- show draw marker on in game while prospecting
Config.DetectorItem = "detector1"

-- OPTIMIZED: Single location with efficient detection system
Config.Zones = {
    [1] = {
        coords = vector3(2141.6426, 3835.1187, 30.3905),
        data = "mining_site",
        zoneSize = 200, -- Optimized zone size for better coverage
        zoneLocations = 150, -- Reduced for better performance
        name = "Sandy Shores Mining Site",
        blipSprite = 618, -- Mining blip
        blipColor = 46, -- Dark yellow
    },
}

Config.DefaultItems = {
    [1] = {name = "steel", min = 1, max = 2}
} -- will be selected if you dont put the common, rare and epic items in the config

-- OPTIMIZED: Level-based reward system - setiap level detector dapat reward berbeda
Config.Items = {
    ["mining_site"] = {
        -- Level 1 Detector Rewards
        ["level1"] = {
            ["common"] = {
                [1] = {name = "money", min = 100, max = 200},
                [2] = {name = "metalscrap", min = 1, max = 2},
            },
            ["rare"] = {
                [1] = {name = "money", min = 300, max = 500},
            },
            ["epic"] = {
                [1] = {name = "money", min = 800, max = 1000},
            }
        },
        -- Level 2 Detector Rewards
        ["level2"] = {
            ["common"] = {
                [1] = {name = "steel", min = 2, max = 4},
                [2] = {name = "metalscrap", min = 2, max = 3},
            },
            ["rare"] = {
                [1] = {name = "steel", min = 5, max = 8},
                [2] = {name = "money", min = 400, max = 600},
            },
            ["epic"] = {
                [1] = {name = "steel", min = 10, max = 15},
            }
        },
        -- Level 3 Detector Rewards
        ["level3"] = {
            ["common"] = {
                [1] = {name = "copper", min = 3, max = 5},
                [2] = {name = "aluminum", min = 2, max = 4},
            },
            ["rare"] = {
                [1] = {name = "copper", min = 8, max = 12},
                [2] = {name = "steel", min = 6, max = 10},
            },
            ["epic"] = {
                [1] = {name = "copper", min = 15, max = 20},
                [2] = {name = "money", min = 1200, max = 1500},
            }
        },
        -- Level 4 Detector Rewards
        ["level4"] = {
            ["common"] = {
                [1] = {name = "gold", min = 1, max = 2},
                [2] = {name = "silver", min = 2, max = 4},
            },
            ["rare"] = {
                [1] = {name = "gold", min = 3, max = 5},
                [2] = {name = "diamond", min = 1, max = 2},
            },
            ["epic"] = {
                [1] = {name = "gold", min = 8, max = 12},
                [2] = {name = "diamond", min = 3, max = 5},
            }
        },
        -- Level 5 Detector Rewards (BEST)
        ["level5"] = {
            ["common"] = {
                [1] = {name = "diamond", min = 2, max = 4},
                [2] = {name = "gold", min = 3, max = 6},
            },
            ["rare"] = {
                [1] = {name = "diamond", min = 5, max = 8},
                [2] = {name = "money", min = 2000, max = 3000},
            },
            ["epic"] = {
                [1] = {name = "diamond", min = 10, max = 15},
                [2] = {name = "money", min = 5000, max = 8000},
                [3] = {name = "goldbar", min = 1, max = 2},
            }
        },
    },
}

-- OPTIMIZED: Detector configurations for levels 1-5
Config.Detectors = {
    ["detector1"] = {
        name = "Basic Metal Detector",
        level = 1,
        multiplier = 1.0,
        bonusChance = 0,
        range = 50,
        description = "Entry level detector for beginners"
    },
    ["detector2"] = {
        name = "Advanced Metal Detector",
        level = 2,
        multiplier = 1.2,
        bonusChance = 5,
        range = 75,
        description = "Improved sensitivity and range"
    },
    ["detector3"] = {
        name = "Professional Metal Detector",
        level = 3,
        multiplier = 1.5,
        bonusChance = 10,
        range = 100,
        description = "Professional grade equipment"
    },
    ["detector4"] = {
        name = "Expert Metal Detector",
        level = 4,
        multiplier = 1.8,
        bonusChance = 15,
        range = 125,
        description = "Expert level with enhanced capabilities"
    },
    ["detector5"] = {
        name = "Master Metal Detector",
        level = 5,
        multiplier = 2.0,
        bonusChance = 20,
        range = 150,
        description = "Ultimate detector for treasure hunters"
    },
}
