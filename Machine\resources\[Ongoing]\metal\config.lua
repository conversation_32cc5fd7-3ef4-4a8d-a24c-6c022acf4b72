-----------------For support, scripts, and more----------------
--------------- https://discord.gg/VGYkkAYVv2  -------------
---------------------------------------------------------------

Config = {}

Config.Core = "ESX" -- ESX or QBCore
Config.PlayerLoadedEvent = "esx:playerLoaded" -- esx:playerLoaded || QBCore:Client:OnPlayerLoaded

Config.ShowBlip = true -- show blip on map

Config.Chances = {
    ["common"] = 100, -- 100%
    ["rare"] = 15, -- 15%
    ["epic"] = 5, -- 5%
}
Config.ShowDrawMaker = true -- show draw marker on in game while prospecting
Config.DetectorItem = "detector1"

-- OPTIMIZED: Single location with efficient detection system
Config.Zones = {
    [1] = {
        coords = vector3(2141.6426, 3835.1187, 30.3905),
        data = "mining_site",
        zoneSize = 200, -- Optimized zone size for better coverage
        zoneLocations = 150, -- Reduced for better performance
        name = "Sandy Shores Mining Site",
        blipSprite = 618, -- Mining blip
        blipColor = 46, -- Dark yellow
    },
}

Config.DefaultItems = {
    [1] = {name = "steel", min = 1, max = 2}
} -- will be selected if you dont put the common, rare and epic items in the config

-- OPTIMIZED: Level-based reward system - setiap level detector dapat reward berbeda
Config.Items = {
    ["mining_site"] = {
        -- Level 1 Detector Rewards
        ["level1"] = {
            ["common"] = {
                [1] = {name = "money", min = 100, max = 200},
                [2] = {name = "metalscrap", min = 1, max = 2},
            },
            ["rare"] = {
                [1] = {name = "money", min = 300, max = 500},
            },
            ["epic"] = {
                [1] = {name = "money", min = 800, max = 1000},
            }
        },
        -- Level 2 Detector Rewards
        ["level2"] = {
            ["common"] = {
                [1] = {name = "steel", min = 2, max = 4},
                [2] = {name = "metalscrap", min = 2, max = 3},
            },
            ["rare"] = {
                [1] = {name = "steel", min = 5, max = 8},
                [2] = {name = "money", min = 400, max = 600},
            },
            ["epic"] = {
                [1] = {name = "steel", min = 10, max = 15},
            }
        },
        -- Level 3 Detector Rewards
        ["level3"] = {
            ["common"] = {
                [1] = {name = "copper", min = 3, max = 5},
                [2] = {name = "aluminum", min = 2, max = 4},
            },
            ["rare"] = {
                [1] = {name = "copper", min = 8, max = 12},
                [2] = {name = "steel", min = 6, max = 10},
            },
            ["epic"] = {
                [1] = {name = "copper", min = 15, max = 20},
                [2] = {name = "money", min = 1200, max = 1500},
            }
        },
        -- Level 4 Detector Rewards
        ["level4"] = {
            ["common"] = {
                [1] = {name = "gold", min = 1, max = 2},
                [2] = {name = "silver", min = 2, max = 4},
            },
            ["rare"] = {
                [1] = {name = "gold", min = 3, max = 5},
                [2] = {name = "diamond", min = 1, max = 2},
            },
            ["epic"] = {
                [1] = {name = "gold", min = 8, max = 12},
                [2] = {name = "diamond", min = 3, max = 5},
            }
        },
        -- Level 5 Detector Rewards (BEST)
        ["level5"] = {
            ["common"] = {
                [1] = {name = "diamond", min = 2, max = 4},
                [2] = {name = "gold", min = 3, max = 6},
            },
            ["rare"] = {
                [1] = {name = "diamond", min = 5, max = 8},
                [2] = {name = "money", min = 2000, max = 3000},
            },
            ["epic"] = {
                [1] = {name = "diamond", min = 10, max = 15},
                [2] = {name = "money", min = 5000, max = 8000},
                [3] = {name = "goldbar", min = 1, max = 2},
            }
        },
    },
}

-- SIMPLIFIED: Detector names for level identification (using existing DetectorItem system)
Config.DetectorNames = {
    ["detector1"] = "Basic Metal Detector",
    ["detector2"] = "Advanced Metal Detector",
    ["detector3"] = "Professional Metal Detector",
    ["detector4"] = "Expert Metal Detector",
    ["detector5"] = "Master Metal Detector",
}
