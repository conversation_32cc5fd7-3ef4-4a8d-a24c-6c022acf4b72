-----------------For support, scripts, and more----------------
--------------- https://discord.gg/VGYkkAYVv2  -------------
---------------------------------------------------------------

Config = {}

-- ESX Framework Configuration
Config.Core = "ESX" -- ESX or QBCore
Config.PlayerLoadedEvent = "esx:playerLoaded" -- esx:playerLoaded || QBCore:Client:OnPlayerLoaded

Config.ShowBlip = true -- show blip on map

-- Base chances for different rarity levels
Config.Chances = {
    ["common"] = 100, -- 100%
    ["rare"] = 15, -- 15%
    ["epic"] = 5, -- 5%
}

Config.ShowDrawMaker = true -- show draw marker on in game while prospecting

-- Multiple detector types with different effectiveness
Config.Detectors = {
    ["detector1"] = {
        name = "Basic Metal Detector",
        multiplier = 1.0, -- Base multiplier
        bonusChance = 0, -- No bonus chance
        maxDistance = 50, -- Detection range
    },
    ["detector2"] = {
        name = "Advanced Metal Detector",
        multiplier = 1.2, -- 20% more items
        bonusChance = 5, -- 5% bonus chance for rare items
        maxDistance = 75,
    },
    ["detector3"] = {
        name = "Professional Metal Detector",
        multiplier = 1.5, -- 50% more items
        bonusChance = 10, -- 10% bonus chance for rare items
        maxDistance = 100,
    },
    ["detector4"] = {
        name = "Expert Metal Detector",
        multiplier = 1.8, -- 80% more items
        bonusChance = 15, -- 15% bonus chance for rare items
        maxDistance = 125,
    },
    ["detector5"] = {
        name = "Master Metal Detector",
        multiplier = 2.0, -- 100% more items
        bonusChance = 20, -- 20% bonus chance for rare items
        maxDistance = 150,
    }
}

-- Single optimized zone with multiple detection points
Config.Zones = {
    [1] = {
        coords = vector3(2141.6426, 3835.1187, 30.3905),
        data = "mining_site",
        zoneSize = 150, -- Larger zone for better coverage
        zoneLocations = 300, -- More detection points
        name = "Sandy Shores Mining Site",
        blipSprite = 618, -- Mining blip
        blipColor = 46, -- Dark yellow
    },
}

Config.DefaultItems = {
    [1] = {name = "metalscrap", min = 1, max = 2}
} -- Fallback items if specific location items aren't configured

-- Enhanced reward system with detector-specific bonuses
Config.Items = {
    ["mining_site"] = {
        ["common"] = {
            [1] = {name = "metalscrap", min = 3, max = 8},
            [2] = {name = "steel", min = 2, max = 6},
            [3] = {name = "iron", min = 2, max = 5},
            [4] = {name = "copper", min = 1, max = 4},
        },
        ["rare"] = {
            [1] = {name = "aluminum", min = 2, max = 4},
            [2] = {name = "gold", min = 1, max = 2},
            [3] = {name = "silver", min = 1, max = 3},
            [4] = {name = "electronics", min = 1, max = 2},
        },
        ["epic"] = {
            [1] = {name = "diamond", min = 1, max = 1},
            [2] = {name = "emerald", min = 1, max = 1},
            [3] = {name = "ruby", min = 1, max = 1},
            [4] = {name = "sapphire", min = 1, max = 1},
        },
    },
}

-- ESX-specific settings
Config.ESX = {
    UseOldESX = false, -- Set to true if using ESX 1.1 or older
    InventorySystem = "ox_inventory", -- default, ox_inventory, qs-inventory
    NotificationSystem = "ox_lib", -- default, ox_lib, okokNotify
}

-- Animation and timing settings
Config.Animation = {
    dict = "amb@world_human_hammering@male@base",
    anim = "base",
    duration = 5000, -- 5 seconds base detection time
}

-- Sound effects (optional)
Config.Sounds = {
    enabled = true,
    detectionSound = "PICK_UP",
    successSound = "WAYPOINT_SET",
    failSound = "ERROR",
}
